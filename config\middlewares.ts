export default [
  "strapi::logger",
  "strapi::errors",
  "strapi::security",
  "strapi::cors",
  "strapi::poweredBy",
  "strapi::query",
  {
    name: "strapi::body",
    config: {
      formLimit: "256mb", // modify form body
      jsonLimit: "256mb", // modify JSON body
      textLimit: "256mb", // modify text body
      formidable: {
        maxFileSize: 10 * 1024 * 1024, // 10MB limit for better stability
        maxFiles: 10, // Maximum number of files
        maxFieldsSize: 20 * 1024 * 1024, // 20MB for form fields
        keepExtensions: true, // Keep file extensions
        multiples: true, // Allow multiple files
        timeout: 60000, // 60 seconds timeout
      },
    },
  },
  "strapi::session",
  "strapi::favicon",
  { resolve: "./src/middlewares/admin-redirect" },
  {
    resolve: "./src/middlewares/zalo-security",
    config: {
      validateSignature: false, // Set to true in production
    },
  },
  "strapi::public",
];
