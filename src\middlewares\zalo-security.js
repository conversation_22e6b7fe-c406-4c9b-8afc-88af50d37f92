"use strict";

const { ApplicationError } = require('@strapi/utils').errors;
const zaloService = require('../services/zalo-service');

/**
 * Zalo Mini App Security Middleware
 * Validates requests from Zalo Mini App and prevents unauthorized access
 */
module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    try {
      // Skip validation for non-Zalo routes
      if (!ctx.request.url.includes('/auth/zalo') && !ctx.request.url.includes('/auth/link-zalo')) {
        return await next();
      }

      const { headers, body } = ctx.request;
      
      // Basic security checks
      await performSecurityChecks(ctx, headers, body);
      
      // Rate limiting for Zalo auth endpoints
      await checkRateLimit(ctx);
      
      // Validate request signature if configured
      if (config.validateSignature) {
        await validateZaloSignature(ctx, headers, body);
      }
      
      // Log security event
      logSecurityEvent(ctx, 'zalo_request_validated');
      
      await next();
      
    } catch (error) {
      console.error('Zalo security middleware error:', error);
      
      // Log security violation
      logSecurityEvent(ctx, 'zalo_security_violation', {
        error: error.message,
        ip: ctx.request.ip,
        userAgent: ctx.request.headers['user-agent']
      });
      
      if (error instanceof ApplicationError) {
        throw error;
      }
      
      throw new ApplicationError('Yêu cầu không hợp lệ từ Zalo Mini App');
    }
  };
};

/**
 * Perform basic security checks
 */
async function performSecurityChecks(ctx, headers, body) {
  // Check required headers
  const userAgent = headers['user-agent'];
  if (!userAgent) {
    throw new ApplicationError('Missing User-Agent header');
  }
  
  // Validate content type for POST requests
  if (ctx.method === 'POST') {
    const contentType = headers['content-type'];
    if (!contentType || !contentType.includes('application/json')) {
      throw new ApplicationError('Invalid content type');
    }
  }
  
  // Check for required fields in body
  if (ctx.method === 'POST' && body) {
    if (!body.accessToken) {
      throw new ApplicationError('Access token là bắt buộc');
    }
    
    // Validate access token format
    if (typeof body.accessToken !== 'string' || body.accessToken.length < 10) {
      throw new ApplicationError('Access token không hợp lệ');
    }
  }
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /eval\s*\(/i,
    /expression\s*\(/i
  ];
  
  const requestString = JSON.stringify(body);
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestString)) {
      throw new ApplicationError('Phát hiện nội dung nguy hiểm trong yêu cầu');
    }
  }
}

/**
 * Check rate limiting for Zalo auth endpoints
 */
async function checkRateLimit(ctx) {
  const ip = ctx.request.ip;
  const key = `zalo_auth_rate_limit:${ip}`;
  
  try {
    // Get current count from cache/memory
    const currentCount = await getCacheValue(key) || 0;
    const maxRequests = 10; // Max 10 requests per minute
    const windowMs = 60 * 1000; // 1 minute
    
    if (currentCount >= maxRequests) {
      throw new ApplicationError('Quá nhiều yêu cầu. Vui lòng thử lại sau.');
    }
    
    // Increment counter
    await setCacheValue(key, currentCount + 1, windowMs);
    
  } catch (error) {
    if (error instanceof ApplicationError) {
      throw error;
    }
    // If cache fails, continue but log warning
    console.warn('Rate limit check failed:', error);
  }
}

/**
 * Validate Zalo request signature
 */
async function validateZaloSignature(ctx, headers, body) {
  try {
    const signature = headers['x-zalo-signature'];
    
    if (!signature) {
      console.warn('No Zalo signature provided, skipping validation');
      return;
    }
    
    const isValid = await zaloService.verifySignature(body, signature);
    
    if (!isValid) {
      throw new ApplicationError('Chữ ký Zalo không hợp lệ');
    }
    
  } catch (error) {
    console.error('Signature validation error:', error);
    throw new ApplicationError('Lỗi xác thực chữ ký Zalo');
  }
}

/**
 * Simple cache implementation (you can replace with Redis in production)
 */
const cache = new Map();

async function getCacheValue(key) {
  const item = cache.get(key);
  if (!item) return null;
  
  if (Date.now() > item.expiry) {
    cache.delete(key);
    return null;
  }
  
  return item.value;
}

async function setCacheValue(key, value, ttlMs) {
  cache.set(key, {
    value,
    expiry: Date.now() + ttlMs
  });
}

/**
 * Log security events for monitoring
 */
function logSecurityEvent(ctx, event, metadata = {}) {
  const logData = {
    event,
    timestamp: new Date().toISOString(),
    ip: ctx.request.ip,
    userAgent: ctx.request.headers['user-agent'],
    url: ctx.request.url,
    method: ctx.method,
    ...metadata
  };
  
  console.log('Zalo Security Event:', JSON.stringify(logData));
  
  // You can extend this to send to monitoring service
  // Example: send to Sentry, DataDog, etc.
}

/**
 * Clean up expired cache entries periodically
 */
setInterval(() => {
  const now = Date.now();
  for (const [key, item] of cache.entries()) {
    if (now > item.expiry) {
      cache.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean every 5 minutes
